<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Debug information
$debug = [];
$debug['database_config'] = [
    'server' => $servername,
    'username' => $username,
    'database' => $dbname
];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    $debug['connection_error'] = $conn->connect_error;
    die(json_encode([
        "error" => "Connection failed: " . $conn->connect_error,
        "debug" => $debug
    ]));
}

$debug['connection'] = 'Connected successfully';

// Check if the database exists
$dbCheck = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
if ($dbCheck->num_rows == 0) {
    $debug['databases'] = [];
    $result = $conn->query("SHOW DATABASES");
    while ($row = $result->fetch_assoc()) {
        $debug['databases'][] = $row['Database'];
    }

    die(json_encode([
        "error" => "Database '$dbname' does not exist",
        "debug" => $debug
    ]));
}

// Check if the table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($tableCheck->num_rows == 0) {
    $debug['tables'] = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_assoc()) {
        $debug['tables'][] = $row["Tables_in_$dbname"];
    }

    die(json_encode([
        "error" => "Table 'electrical_data' does not exist in the database",
        "debug" => $debug
    ]));
}

$debug['table_exists'] = true;

// Check table structure
$debug['table_structure'] = [];
$result = $conn->query("DESCRIBE electrical_data");
while ($row = $result->fetch_assoc()) {
    $debug['table_structure'][] = $row;
}

// Check if there's any data in the table
$dataCheck = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$dataCount = $dataCheck->fetch_assoc()['count'];
$debug['record_count'] = $dataCount;

if ($dataCount == 0) {
    die(json_encode([
        "error" => "No data found in the 'electrical_data' table",
        "debug" => $debug
    ]));
}

// Get query parameters
$startDate = isset($_GET['start']) ? $_GET['start'] : date('Y-m-d H:i:s', strtotime('-24 hours'));
$endDate = isset($_GET['end']) ? $_GET['end'] : date('Y-m-d H:i:s');
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 25;
$offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

// Debug information
$debug = [];
$debug['original_start'] = $startDate;
$debug['original_end'] = $endDate;

// Try to parse and format the dates properly
try {
    // If the date is in ISO format (with T), convert it to MySQL format
    if (strpos($startDate, 'T') !== false) {
        $startDate = date('Y-m-d H:i:s', strtotime($startDate));
    }
    if (strpos($endDate, 'T') !== false) {
        $endDate = date('Y-m-d H:i:s', strtotime($endDate));
    }

    // Ensure dates are in the correct format
    $startDate = date('Y-m-d H:i:s', strtotime($startDate));
    $endDate = date('Y-m-d H:i:s', strtotime($endDate));

    // Ensure start time is always before end time (swap if needed)
    if (strtotime($startDate) > strtotime($endDate)) {
        $temp = $startDate;
        $startDate = $endDate;
        $endDate = $temp;
        $debug['dates_swapped'] = true;
    }
} catch (Exception $e) {
    // If there's an error parsing dates, use defaults
    $startDate = date('Y-m-d H:i:s', strtotime('-24 hours'));
    $endDate = date('Y-m-d H:i:s');
    $debug['date_parse_error'] = $e->getMessage();
}

$debug['formatted_start'] = $startDate;
$debug['formatted_end'] = $endDate;

// Validate limit - allow much larger limits to get ALL data points
if ($limit <= 0) {
    $limit = 100000; // Default to a very large limit to get all data
} else if ($limit > 1000000) {
    $limit = 1000000; // Cap at 1 million records for safety
}

// Validate offset
if ($offset < 0) {
    $offset = 0;
}

$debug['limit'] = $limit;
$debug['offset'] = $offset;

// Add query information to debug
$debug['query_params'] = [
    'start_date' => $startDate,
    'end_date' => $endDate,
    'limit' => $limit,
    'offset' => $offset
];

// Try a simple query first to test if we can access the data
$testQuery = "SELECT * FROM electrical_data LIMIT 1";
$testResult = $conn->query($testQuery);
if (!$testResult) {
    $debug['test_query_error'] = $conn->error;
    die(json_encode([
        "error" => "Error executing test query: " . $conn->error,
        "debug" => $debug
    ]));
}

$debug['test_query_success'] = true;
$debug['test_query_result'] = [];
while ($row = $testResult->fetch_assoc()) {
    $debug['test_query_result'][] = $row;
}

// Prepare the query with parameters
// Note: We're using ASC order to get oldest data first for proper chart display
try {
    $query = "SELECT * FROM electrical_data WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp ASC LIMIT ? OFFSET ?";
    $debug['main_query'] = $query;

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $bindResult = $stmt->bind_param("ssii", $startDate, $endDate, $limit, $offset);
    if (!$bindResult) {
        throw new Exception("Binding parameters failed: " . $stmt->error);
    }

    $executeResult = $stmt->execute();
    if (!$executeResult) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Getting result failed: " . $stmt->error);
    }

    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as total FROM electrical_data WHERE timestamp BETWEEN ? AND ?";
    $debug['count_query'] = $countQuery;

    $countStmt = $conn->prepare($countQuery);
    if (!$countStmt) {
        throw new Exception("Prepare count query failed: " . $conn->error);
    }

    $bindCountResult = $countStmt->bind_param("ss", $startDate, $endDate);
    if (!$bindCountResult) {
        throw new Exception("Binding count parameters failed: " . $countStmt->error);
    }

    $executeCountResult = $countStmt->execute();
    if (!$executeCountResult) {
        throw new Exception("Execute count query failed: " . $countStmt->error);
    }

    $countResult = $countStmt->get_result();
    if (!$countResult) {
        throw new Exception("Getting count result failed: " . $countStmt->error);
    }

    $totalCount = $countResult->fetch_assoc()['total'];
    $debug['total_count'] = $totalCount;

} catch (Exception $e) {
    $debug['query_error'] = $e->getMessage();

    // Try a direct query without prepared statement as fallback
    $debug['fallback'] = "Using direct query as fallback";
    $safeStartDate = $conn->real_escape_string($startDate);
    $safeEndDate = $conn->real_escape_string($endDate);

    $directQuery = "SELECT * FROM electrical_data WHERE timestamp BETWEEN '$safeStartDate' AND '$safeEndDate' ORDER BY timestamp ASC LIMIT $limit OFFSET $offset";
    $debug['direct_query'] = $directQuery;

    $result = $conn->query($directQuery);
    if (!$result) {
        die(json_encode([
            "error" => "Error executing direct query: " . $conn->error,
            "debug" => $debug
        ]));
    }

    $countDirectQuery = "SELECT COUNT(*) as total FROM electrical_data WHERE timestamp BETWEEN '$safeStartDate' AND '$safeEndDate'";
    $debug['direct_count_query'] = $countDirectQuery;

    $countResult = $conn->query($countDirectQuery);
    if (!$countResult) {
        die(json_encode([
            "error" => "Error executing direct count query: " . $conn->error,
            "debug" => $debug
        ]));
    }

    $totalCount = $countResult->fetch_assoc()['total'];
    $debug['total_count'] = $totalCount;
}

// Fetch all rows
$data = [];
$debug['raw_data_sample'] = [];
$rowCount = 0;

while ($row = $result->fetch_assoc()) {
    // Add first few rows to debug for inspection
    if ($rowCount < 3) {
        $debug['raw_data_sample'][] = $row;
    }
    $rowCount++;

    // Safely get values with error handling
    $safeGet = function($array, $key, $default = 0) {
        if (!isset($array[$key]) || is_null($array[$key]) || $array[$key] === '') {
            return $default;
        }
        return $array[$key];
    };

    // Format timestamp
    $timestamp = $safeGet($row, "timestamp", date('Y-m-d H:i:s'));

    // Create a properly formatted data row
    $dataRow = [
        "id" => intval($safeGet($row, "id", 0)),
        "voltage_1" => floatval($safeGet($row, "voltage_1", 0)),
        "voltage_2" => floatval($safeGet($row, "voltage_2", 0)),
        "voltage_3" => floatval($safeGet($row, "voltage_3", 0)),
        "current_1" => floatval($safeGet($row, "current_1", 0)),
        "current_2" => floatval($safeGet($row, "current_2", 0)),
        "current_3" => floatval($safeGet($row, "current_3", 0)),
        "pf_1" => floatval($safeGet($row, "pf_1", 0)),
        "pf_2" => floatval($safeGet($row, "pf_2", 0)),
        "pf_3" => floatval($safeGet($row, "pf_3", 0)),
        "kva_1" => floatval($safeGet($row, "kva_1", 0)),
        "kva_2" => floatval($safeGet($row, "kva_2", 0)),
        "kva_3" => floatval($safeGet($row, "kva_3", 0)),
        "total_kva" => floatval($safeGet($row, "total_kva", 0)),
        "total_kw" => floatval($safeGet($row, "total_kw", 0)),
        "total_kvar" => floatval($safeGet($row, "total_kvar", 0)),
        "frequency" => floatval($safeGet($row, "frequency", 0)),
        "timestamp" => $timestamp
    ];

    $data[] = $dataRow;
}

// Add row count to debug
$debug['actual_row_count'] = $rowCount;

// Add query information to debug
$debug['query'] = "SELECT * FROM electrical_data WHERE timestamp BETWEEN '{$startDate}' AND '{$endDate}' ORDER BY timestamp ASC LIMIT {$limit} OFFSET {$offset}";
$debug['total_count'] = intval($totalCount);
$debug['data_count'] = count($data);

// Add final debug information
$debug['data_count'] = count($data);
$debug['json_encode_options'] = JSON_PRETTY_PRINT;

// Add time information to debug
$debug['time_info'] = [
    'start_timestamp' => strtotime($startDate),
    'end_timestamp' => strtotime($endDate),
    'current_timestamp' => time(),
    'current_time' => date('Y-m-d H:i:s')
];

// Prepare the response
$response = [
    "data" => $data,
    "total" => intval($totalCount),
    "limit" => $limit,
    "offset" => $offset,
    "page" => floor($offset / $limit) + 1,
    "pages" => ceil($totalCount / $limit),
    "start_time" => $startDate,
    "end_time" => $endDate,
    "debug" => $debug
];

// Check for empty data
if (empty($data)) {
    $response["warning"] = "No data found for the specified criteria";
}

// Return the data as JSON with error handling
try {
    // Use JSON_PRETTY_PRINT for debugging, but remove in production for smaller payload
    $jsonResponse = json_encode($response, JSON_PRETTY_PRINT);

    if ($jsonResponse === false) {
        throw new Exception(json_last_error_msg());
    }

    echo $jsonResponse;
} catch (Exception $e) {
    // If JSON encoding fails, return a simplified response
    echo json_encode([
        "error" => "Error encoding JSON response: " . $e->getMessage(),
        "data" => [],
        "debug" => [
            "json_error" => json_last_error(),
            "json_error_msg" => json_last_error_msg(),
            "data_count" => count($data),
            "data_sample" => array_slice($data, 0, 1)
        ]
    ]);
}

// Close the connection
$stmt->close();
$countStmt->close();
$conn->close();
?>
