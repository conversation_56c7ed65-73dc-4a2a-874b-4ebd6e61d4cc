<?php
// Set headers for JSON response and CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    echo json_encode(["error" => "Connection failed: " . $conn->connect_error]);
    exit;
}

// Get the latest records from the database (last 10 seconds of data)
// Check if a specific number of records is requested
$num_records = isset($_GET['records']) ? intval($_GET['records']) : 1;

// Ensure we have a reasonable limit but allow more records for smoother updates
if ($num_records <= 0 || $num_records > 500) {
    $num_records = 20; // Default to 20 records for better initial display
}

// Get the latest records
$sql = "SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT " . $num_records;
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    // If only one record is requested, return it directly
    if ($num_records == 1) {
        $row = $result->fetch_assoc();

        // Convert timestamp to IST
        $timestamp = new DateTime($row["timestamp"]);
        $timestamp_ist = $timestamp->format('Y-m-d H:i:s');

        // Format the response
        $response = [
            "voltage_1" => floatval($row["voltage_1"]),
            "voltage_2" => floatval($row["voltage_2"]),
            "voltage_3" => floatval($row["voltage_3"]),
            "current_1" => floatval($row["current_1"]),
            "current_2" => floatval($row["current_2"]),
            "current_3" => floatval($row["current_3"]),
            "pf_1" => floatval($row["pf_1"]),
            "pf_2" => floatval($row["pf_2"]),
            "pf_3" => floatval($row["pf_3"]),
            "kva_1" => floatval($row["kva_1"]),
            "kva_2" => floatval($row["kva_2"]),
            "kva_3" => floatval($row["kva_3"]),
            "total_kva" => floatval($row["total_kva"]),
            "total_kw" => floatval($row["total_kw"]),
            "total_kvar" => floatval($row["total_kvar"]),
            "frequency" => floatval($row["frequency"]),
            "timestamp" => $timestamp_ist
        ];
    } else {
        // Multiple records requested, return an array
        $response = [];
        while ($row = $result->fetch_assoc()) {
            // Convert timestamp to IST
            $timestamp = new DateTime($row["timestamp"]);
            $timestamp_ist = $timestamp->format('Y-m-d H:i:s');

            // Format each record
            $response[] = [
                "voltage_1" => floatval($row["voltage_1"]),
                "voltage_2" => floatval($row["voltage_2"]),
                "voltage_3" => floatval($row["voltage_3"]),
                "current_1" => floatval($row["current_1"]),
                "current_2" => floatval($row["current_2"]),
                "current_3" => floatval($row["current_3"]),
                "pf_1" => floatval($row["pf_1"]),
                "pf_2" => floatval($row["pf_2"]),
                "pf_3" => floatval($row["pf_3"]),
                "kva_1" => floatval($row["kva_1"]),
                "kva_2" => floatval($row["kva_2"]),
                "kva_3" => floatval($row["kva_3"]),
                "total_kva" => floatval($row["total_kva"]),
                "total_kw" => floatval($row["total_kw"]),
                "total_kvar" => floatval($row["total_kvar"]),
                "frequency" => floatval($row["frequency"]),
                "timestamp" => $timestamp_ist
            ];
        }
    }

    echo json_encode($response);
} else {
    // If no data found, return dummy data for testing
    $current_time = date('Y-m-d H:i:s');
    $dummy_data = [
        "voltage_1" => 400.5,
        "voltage_2" => 401.2,
        "voltage_3" => 399.8,
        "current_1" => 50.1,
        "current_2" => 49.8,
        "current_3" => 50.3,
        "pf_1" => 0.92,
        "pf_2" => 0.93,
        "pf_3" => 0.91,
        "kva_1" => 20.1,
        "kva_2" => 19.8,
        "kva_3" => 20.3,
        "total_kva" => 60.2,
        "total_kw" => 55.4,
        "total_kvar" => 24.8,
        "frequency" => 50.1,
        "timestamp" => $current_time
    ];

    echo json_encode($dummy_data);
}

$conn->close();
?>