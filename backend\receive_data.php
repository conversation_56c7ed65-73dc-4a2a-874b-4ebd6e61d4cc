<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die(json_encode(["status" => "error", "message" => "Connection failed: " . $conn->connect_error]));
}

// Log request method and headers for debugging
$requestMethod = $_SERVER['REQUEST_METHOD'];
file_put_contents('esp32_request_log.txt', date('Y-m-d H:i:s') . " - Method: $requestMethod\n", FILE_APPEND);

// Get the raw POST data
$rawInput = file_get_contents('php://input');
file_put_contents('esp32_request_log.txt', date('Y-m-d H:i:s') . " - Raw input: $rawInput\n", FILE_APPEND);

// Accept both POST and GET for testing
if ($requestMethod === 'POST') {
    $data = json_decode($rawInput, true);
} elseif ($requestMethod === 'GET') {
    // For testing with GET requests
    $data = [
        'voltage_1' => 400,
        'voltage_2' => 401,
        'voltage_3' => 399,
        'current_1' => 10,
        'current_2' => 11,
        'current_3' => 9.5,
        'pf_1' => 0.95,
        'pf_2' => 0.94,
        'pf_3' => 0.96,
        'kva_1' => 20,
        'kva_2' => 21,
        'kva_3' => 19,
        'total_kva' => 60,
        'total_kw' => 57,
        'total_kvar' => 18,
        'frequency' => 50.1
    ];
}

if ($data) {
    // Extract three-phase electrical parameters from the request
    $voltage_1 = isset($data['voltage_1']) ? $data['voltage_1'] : null;
    $voltage_2 = isset($data['voltage_2']) ? $data['voltage_2'] : null;
    $voltage_3 = isset($data['voltage_3']) ? $data['voltage_3'] : null;

    $current_1 = isset($data['current_1']) ? $data['current_1'] : null;
    $current_2 = isset($data['current_2']) ? $data['current_2'] : null;
    $current_3 = isset($data['current_3']) ? $data['current_3'] : null;

    $pf_1 = isset($data['pf_1']) ? $data['pf_1'] : null;
    $pf_2 = isset($data['pf_2']) ? $data['pf_2'] : null;
    $pf_3 = isset($data['pf_3']) ? $data['pf_3'] : null;

    $kva_1 = isset($data['kva_1']) ? $data['kva_1'] : null;
    $kva_2 = isset($data['kva_2']) ? $data['kva_2'] : null;
    $kva_3 = isset($data['kva_3']) ? $data['kva_3'] : null;

    $total_kva = isset($data['total_kva']) ? $data['total_kva'] : null;
    $total_kw = isset($data['total_kw']) ? $data['total_kw'] : null;
    $total_kvar = isset($data['total_kvar']) ? $data['total_kvar'] : null;

    $frequency = isset($data['frequency']) ? $data['frequency'] : null;
    $timestamp = date('Y-m-d H:i:s');

    // Prepare and execute the SQL statement
    $stmt = $conn->prepare("INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $stmt->bind_param("dddddddddddddddds",
        $voltage_1, $voltage_2, $voltage_3,
        $current_1, $current_2, $current_3,
        $pf_1, $pf_2, $pf_3,
        $kva_1, $kva_2, $kva_3,
        $total_kva, $total_kw, $total_kvar,
        $frequency, $timestamp
    );

    if ($stmt->execute()) {
        echo json_encode(["status" => "success", "message" => "Three-phase electrical data received and stored successfully"]);
    } else {
        echo json_encode(["status" => "error", "message" => "Error storing data: " . $stmt->error]);
    }

    $stmt->close();
} else {
    echo json_encode(["status" => "error", "message" => "No data received"]);
}

$conn->close();
?>