/* Advanced Dashboard Styling */
:root {
    /* Professional color palette */
    --primary-color: #1a73e8;
    --primary-dark: #0d47a1;
    --primary-light: #4285f4;
    --secondary-color: #34a853;
    --accent-color: #ea4335;
    --warning-color: #fbbc04;
    --success-color: #0f9d58;
    --text-color: #202124;
    --text-secondary: #5f6368;
    --background-color: #f8f9fa;
    --card-color: #ffffff;
    --border-color: #dadce0;
    --hover-color: #f1f3f4;
    --shadow-sm: 0 1px 2px 0 rgba(60, 64, 67, 0.1), 0 1px 3px 1px rgba(60, 64, 67, 0.08);
    --shadow-md: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    --shadow-lg: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3);
    --font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    --border-radius: 8px;
    --transition-speed: 0.2s;
    
    /* Dark theme colors (initially not applied) */
    --dark-background: #202124;
    --dark-card: #2d2e31;
    --dark-border: #3c4043;
    --dark-text: #e8eaed;
    --dark-text-secondary: #9aa0a6;
    --dark-hover: #35363a;
}

/* Dark theme */
body.dark-theme {
    --background-color: var(--dark-background);
    --card-color: var(--dark-card);
    --border-color: var(--dark-border);
    --text-color: var(--dark-text);
    --text-secondary: var(--dark-text-secondary);
    --hover-color: var(--dark-hover);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: background-color var(--transition-speed) ease;
    overflow-x: hidden;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background-color: var(--card-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
    transition: all var(--transition-speed) ease;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header span {
    color: var(--primary-color);
    font-size: 24px;
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 4px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 0 24px 24px 0;
    transition: all var(--transition-speed) ease;
    gap: 12px;
    margin-right: 12px;
}

.sidebar-nav a:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
}

.sidebar-nav li.active a {
    background-color: #e8f0fe;
    color: var(--primary-color);
    font-weight: 500;
}

body.dark-theme .sidebar-nav li.active a {
    background-color: rgba(26, 115, 232, 0.12);
}

.sidebar-nav span.material-icons-round {
    font-size: 20px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.theme-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 10px;
    border-radius: 24px;
    cursor: pointer;
    width: 100%;
    transition: all var(--transition-speed) ease;
    margin-bottom: 16px;
}

.theme-toggle:hover {
    background-color: var(--hover-color);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 13px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--accent-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 260px;
    padding: 20px;
    transition: all var(--transition-speed) ease;
}

/* Top Bar */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.page-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.last-updated {
    font-size: 12px;
    color: var(--text-secondary);
}

.top-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.time-navigation {
    display: flex;
    align-items: center;
    background-color: var(--hover-color);
    border-radius: 24px;
    padding: 4px;
    margin-right: 8px;
}

#timeDisplay {
    padding: 6px 12px;
    font-weight: 500;
    color: var(--text-color);
    background-color: var(--card-color);
    border-radius: 20px;
    box-shadow: var(--shadow-sm);
    margin: 0 4px;
    font-size: 13px;
}

.icon-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
}

.control-button {
    padding: 8px 16px;
    border: none;
    border-radius: 24px;
    background-color: var(--hover-color);
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    font-weight: 500;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.control-button:hover {
    background-color: rgba(0, 0, 0, 0.08);
}

.control-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.control-button.primary:hover {
    background-color: var(--primary-dark);
}

.control-button.secondary {
    background-color: var(--secondary-color);
    color: white;
}

.control-button.secondary:hover {
    background-color: #2d9548;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all var(--transition-speed) ease;
    border: 1px solid var(--border-color);
}

.summary-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.summary-card.voltage .card-icon {
    background-color: rgba(26, 115, 232, 0.1);
    color: var(--primary-color);
}

.summary-card.current .card-icon {
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--accent-color);
}

.summary-card.power .card-icon {
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--secondary-color);
}

.summary-card.frequency .card-icon {
    background-color: rgba(251, 188, 4, 0.1);
    color: var(--warning-color);
}

.card-icon span {
    font-size: 24px;
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.card-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.trend-indicator {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
}

.trend-indicator.up {
    color: var(--secondary-color);
}

.trend-indicator.down {
    color: var(--accent-color);
}

.trend-indicator.stable {
    color: var(--warning-color);
}

.trend-indicator span.material-icons-round {
    font-size: 16px;
    margin-right: 4px;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 20px;
}

/* Graph Widget */
.graph-widget {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    height: 400px;
    border: 1px solid var(--border-color);
    position: relative;
}

.graph-widget:hover {
    box-shadow: var(--shadow-md);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
}

.widget-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-title span {
    color: var(--primary-color);
    font-size: 20px;
}

.widget-title h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.widget-controls {
    display: flex;
    gap: 4px;
}

.widget-control {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.widget-control:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
}

.widget-control[data-paused="true"] {
    color: var(--accent-color);
    background-color: rgba(234, 67, 53, 0.1);
}

.widget-control[data-auto-scroll="true"] {
    color: var(--primary-color);
    background-color: rgba(26, 115, 232, 0.1);
}

.widget-content {
    height: calc(100% - 53px);
    position: relative;
}

canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Instant Values */
.instant-values {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    z-index: 10;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(4px);
}

body.dark-theme .instant-values {
    background-color: rgba(45, 46, 49, 0.9);
}

.instant-values div {
    display: flex;
    justify-content: space-between;
    gap: 12px;
}

.instant-values span {
    font-weight: 600;
    color: var(--primary-color);
}

/* Status Indicator */
#statusIndicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 16px;
    background-color: var(--card-color);
    color: var(--text-color);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    z-index: 1000;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: none;
    border: 1px solid var(--border-color);
}

#statusIndicator.error {
    background-color: #fdeded;
    color: var(--accent-color);
    border-color: #f6aea9;
}

#statusIndicator.warning {
    background-color: #fef7e0;
    color: var(--warning-color);
    border-color: #feefc3;
}

#statusIndicator.success {
    background-color: #e6f4ea;
    color: var(--success-color);
    border-color: #ceead6;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    background-color: var(--card-color);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    padding: 12px 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: slideIn 0.3s ease forwards;
    border-left: 4px solid var(--primary-color);
    max-width: 100%;
}

.notification.info {
    border-left-color: var(--primary-color);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification.error {
    border-left-color: var(--accent-color);
}

.notification-icon {
    color: var(--primary-color);
    font-size: 20px;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.error .notification-icon {
    color: var(--accent-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.notification-message {
    font-size: 13px;
    color: var(--text-secondary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--card-color);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalIn 0.3s ease forwards;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background-color: var(--card-color);
    z-index: 1;
}

.modal-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    position: sticky;
    bottom: 0;
    background-color: var(--card-color);
    z-index: 1;
}

.modal-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all var(--transition-speed) ease;
}

.modal-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.modal-button.primary:hover {
    background-color: var(--primary-dark);
}

.modal-button.secondary {
    background-color: var(--hover-color);
    color: var(--text-color);
}

.modal-button.secondary:hover {
    background-color: rgba(0, 0, 0, 0.08);
}

@keyframes modalIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Settings */
.settings-section {
    margin-bottom: 24px;
}

.settings-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
}

.setting-item input[type="number"],
.setting-item input[type="text"],
.setting-item input[type="datetime-local"],
.setting-item select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--card-color);
    transition: all var(--transition-speed) ease;
}

.setting-item input[type="range"] {
    width: 100%;
    height: 6px;
    -webkit-appearance: none;
    background: var(--hover-color);
    border-radius: 3px;
    outline: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.setting-item input:focus,
.setting-item select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.setting-item.checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-item.checkbox input {
    width: 16px;
    height: 16px;
}

.setting-item.checkbox label {
    margin-bottom: 0;
}

/* Export Options */
.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.export-option {
    position: relative;
}

.export-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.export-option label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.export-option input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(26, 115, 232, 0.05);
}

.export-option label span.material-icons-round {
    font-size: 24px;
    color: var(--primary-color);
}

.export-option label h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.export-option label p {
    font-size: 12px;
    color: var(--text-secondary);
}

.date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* Fullscreen Widget */
.fullscreen-widget {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-color);
    z-index: 1001;
    display: none;
    flex-direction: column;
}

.fullscreen-header {
    padding: 16px 24px;
    background-color: var(--card-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fullscreen-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fullscreen-title span {
    color: var(--primary-color);
    font-size: 24px;
}

.fullscreen-title h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.fullscreen-controls {
    display: flex;
    gap: 8px;
}

.fullscreen-control {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.fullscreen-control:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
}

.fullscreen-content {
    flex: 1;
    padding: 24px;
    position: relative;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 64px;
        overflow: hidden;
    }
    
    .sidebar-header h2,
    .sidebar-nav a span:not(.material-icons-round),
    .theme-toggle span:not(.material-icons-round),
    .system-status span:not(.status-indicator) {
        display: none;
    }
    
    .sidebar-header {
        justify-content: center;
        padding: 16px 0;
    }
    
    .sidebar-nav a {
        justify-content: center;
        padding: 12px 0;
        margin-right: 0;
    }
    
    .theme-toggle {
        justify-content: center;
        padding: 10px 0;
    }
    
    .system-status {
        justify-content: center;
    }
    
    .main-content {
        margin-left: 64px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .top-controls {
        width: 100%;
        flex-wrap: wrap;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .instant-values {
        position: static;
        margin-top: 16px;
        width: 100%;
    }
    
    .graph-widget {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 10px;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .date-range {
        grid-template-columns: 1fr;
    }
}
