<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers to allow cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Log connection attempt
$logFile = 'connection_test_log.txt';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Connection test from: " . $_SERVER['REMOTE_ADDR'] . "\n", FILE_APPEND);

// Get server information
$serverInfo = [
    'server_ip' => $_SERVER['SERVER_ADDR'],
    'server_name' => $_SERVER['SERVER_NAME'],
    'remote_addr' => $_SERVER['REMOTE_ADDR'],
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'request_uri' => $_SERVER['REQUEST_URI'],
    'http_user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown',
    'php_version' => phpversion(),
    'timestamp' => date('Y-m-d H:i:s'),
    'timezone' => date_default_timezone_get()
];

// Test database connection
$dbInfo = [];
try {
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "esp32_data";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        $dbInfo['status'] = 'error';
        $dbInfo['message'] = 'Connection failed: ' . $conn->connect_error;
    } else {
        $dbInfo['status'] = 'success';
        $dbInfo['message'] = 'Connected to database successfully';

        // Check if the table exists
        $result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
        $dbInfo['table_exists'] = $result->num_rows > 0;

        $conn->close();
    }
} catch (Exception $e) {
    $dbInfo['status'] = 'error';
    $dbInfo['message'] = 'Exception: ' . $e->getMessage();
}

// Response to indicate the server is accessible
echo json_encode([
    'status' => 'success',
    'message' => 'Server is accessible',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_info' => $serverInfo,
    'database' => $dbInfo
]);
?>
