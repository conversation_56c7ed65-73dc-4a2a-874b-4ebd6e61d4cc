<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Historical Data - Power Monitoring Dashboard</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Professional styling -->
    <link rel="stylesheet" href="professional.css">

    <style>
        .history-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
            color: #555;
        }

        input, select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background-color: #1a73e8;
            color: white;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .button:hover {
            background-color: #1557b0;
        }

        .button-green {
            background-color: #0f9d58;
        }

        .button-green:hover {
            background-color: #0b8043;
        }

        .button-red {
            background-color: #d93025;
        }

        .button-red:hover {
            background-color: #b31412;
        }

        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .button-green:disabled {
            background-color: #8acca7;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .page-info {
            font-size: 14px;
            color: #555;
        }

        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
        }

        .message.error {
            background-color: #fdecea;
            border-color: #fdcfcb;
            color: #d93025;
        }

        .message.success {
            background-color: #e6f4ea;
            border-color: #ceead6;
            color: #0f9d58;
        }

        .message.warning {
            background-color: #fef7e0;
            border-color: #feefc3;
            color: #ea8600;
        }

        .message.info {
            background-color: #e8f0fe;
            border-color: #d2e3fc;
            color: #1a73e8;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .material-icons {
            font-size: 18px;
            margin-right: 4px;
        }

        /* Progress indicator for export */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>
                <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">history</span>
                Historical Data
                <span style="font-size: 14px; color: #6c757d; font-weight: normal;">(Indian Standard Time - IST)</span>
            </h1>
            <div class="controls">
                <a href="pro_dashboard.php" class="button">
                    <span class="material-icons">dashboard</span>
                    Back to Dashboard
                </a>
            </div>
        </header>

        <div id="messageContainer"></div>

        <div class="history-container">
            <div class="filters">
                <div class="filter-group">
                    <label for="startDate">Start Date</label>
                    <input type="datetime-local" id="startDate" name="startDate">
                </div>

                <div class="filter-group">
                    <label for="endDate">End Date</label>
                    <input type="datetime-local" id="endDate" name="endDate">
                </div>

                <div class="filter-group">
                    <label for="limit">Records</label>
                    <select id="limit" name="limit">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button id="filterBtn" class="button">
                        <span class="material-icons">filter_alt</span>
                        Apply Filters
                    </button>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button id="exportBtn" class="button button-green">
                        <span class="material-icons">file_download</span>
                        Export All Data (CSV)
                    </button>
                </div>
            </div>

            <div id="dataTable">
                <table>
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>V1N (V)</th>
                            <th>V2N (V)</th>
                            <th>V3N (V)</th>
                            <th>Ia (A)</th>
                            <th>Ib (A)</th>
                            <th>Ic (A)</th>
                            <th>PF1</th>
                            <th>PF2</th>
                            <th>PF3</th>
                            <th>Total kW</th>
                            <th>Frequency (Hz)</th>
                        </tr>
                    </thead>
                    <tbody id="dataRows">
                        <tr>
                            <td colspan="12" class="loading">Loading data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <button id="prevBtn" class="button button-red" disabled>
                    <span class="material-icons">arrow_back</span>
                    Previous
                </button>
                <span id="pageInfo" class="page-info">Page 1 of 1</span>
                <button id="nextBtn" class="button button-red" disabled>
                    Next
                    <span class="material-icons">arrow_forward</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const elements = {
            startDate: document.getElementById('startDate'),
            endDate: document.getElementById('endDate'),
            limit: document.getElementById('limit'),
            filterBtn: document.getElementById('filterBtn'),
            exportBtn: document.getElementById('exportBtn'),
            dataRows: document.getElementById('dataRows'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            pageInfo: document.getElementById('pageInfo'),
            messageContainer: document.getElementById('messageContainer')
        };

        // State
        const state = {
            currentPage: 1,
            totalPages: 1,
            data: []
        };

        // Utility Functions
        const utils = {
            // Show a message to the user
            showMessage: (message, type = 'info') => {
                elements.messageContainer.innerHTML = `
                    <div class="message ${type}">
                        ${message}
                    </div>
                `;
            },

            // Clear any displayed messages
            clearMessage: () => {
                elements.messageContainer.innerHTML = '';
            },

            // Format date for datetime-local input (in IST - Indian Standard Time)
            formatDateForInput: (date) => {
                // Convert to IST by adding 5 hours and 30 minutes to UTC
                // This is needed because the input expects local time
                const istDate = new Date(date.getTime() + (5.5 * 60 * 60 * 1000));
                return istDate.toISOString().slice(0, 16);
            },

            // Format date for server (YYYY-MM-DD HH:MM:SS)
            formatDateForServer: (dateObj) => {
                if (typeof dateObj === 'string') {
                    dateObj = new Date(dateObj);
                }

                // Format as YYYY-MM-DD HH:MM:SS
                const year = dateObj.getFullYear();
                const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                const day = String(dateObj.getDate()).padStart(2, '0');
                const hours = String(dateObj.getHours()).padStart(2, '0');
                const minutes = String(dateObj.getMinutes()).padStart(2, '0');
                const seconds = String(dateObj.getSeconds()).padStart(2, '0');

                const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                console.log('Formatted date for server:', formattedDate);
                return formattedDate;
            },

            // Format number with 3 decimal places
            formatNumber: (value) => {
                if (value === undefined || value === null) return '0.000';
                try {
                    return parseFloat(value).toFixed(3);
                } catch (e) {
                    return '0.000';
                }
            },

            // Format timestamp for display in Indian Standard Time (IST)
            formatTimestamp: (timestamp) => {
                if (!timestamp) return 'N/A';
                try {
                    // Parse the timestamp
                    const date = new Date(timestamp);
                    if (isNaN(date.getTime())) return timestamp;

                    // Format with explicit IST timezone
                    const options = {
                        timeZone: 'Asia/Kolkata', // Indian Standard Time
                        year: 'numeric',
                        month: 'numeric',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: true
                    };

                    const formattedDate = date.toLocaleString('en-IN', options);
                    return formattedDate;
                } catch (e) {
                    console.error('Error formatting timestamp:', e);
                    return timestamp;
                }
            },

            // Set current time for date range
            setCurrentTime: () => {
                // Get current time
                const now = new Date();
                console.log('Using current time:', now);

                // Set end date to current time
                const endDate = new Date(now);

                // Set start date to 1 hour before current time
                const startDate = new Date(now);
                startDate.setHours(startDate.getHours() - 1);

                console.log('Current time range:',
                    utils.formatDateForServer(startDate), 'to',
                    utils.formatDateForServer(endDate));

                // Format dates for the input fields
                elements.startDate.value = utils.formatDateForInput(startDate);
                elements.endDate.value = utils.formatDateForInput(endDate);

                // Fetch data with the new date range
                api.fetchHistoricalData();
            }
        };

        // API Functions
        const api = {
            // Fetch historical data from the backend
            fetchHistoricalData: async () => {
                try {
                    utils.clearMessage();

                    // Show loading state
                    elements.dataRows.innerHTML = '<tr><td colspan="12" class="loading">Loading data...</td></tr>';

                    // Get the input values
                    const startDate = elements.startDate.value;
                    const endDate = elements.endDate.value;

                    // Validate inputs
                    if (!startDate || !endDate) {
                        utils.showMessage('Please select both start and end dates', 'error');
                        return null;
                    }

                    // Parse dates from input fields
                    const startDateObj = new Date(startDate);
                    const endDateObj = new Date(endDate);

                    console.log('Raw date objects:', startDateObj, endDateObj);

                    // Format dates for server in YYYY-MM-DD HH:MM:SS format
                    const startFormatted = utils.formatDateForServer(startDateObj);
                    const endFormatted = utils.formatDateForServer(endDateObj);

                    console.log('Formatted date range for server:', startFormatted, 'to', endFormatted);

                    // Prepare request parameters
                    const limit = elements.limit.value;
                    const offset = (state.currentPage - 1) * parseInt(limit);
                    const cacheBuster = new Date().getTime();

                    // Build URL
                    const url = `../backend/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=${limit}&offset=${offset}&_=${cacheBuster}`;

                    console.log('Fetching data with URL:', url);

                    // Fetch data
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    // Parse response
                    const responseText = await response.text();
                    console.log('Raw response:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
                    }

                    console.log('Parsed data:', data);

                    // Handle error in response
                    if (data.error) {
                        utils.showMessage(`Error: ${data.error}`, 'error');
                        return null;
                    }

                    // Process data
                    state.data = Array.isArray(data.data) ? data.data : [];
                    state.totalPages = Math.ceil(data.total / parseInt(limit));

                    // Check if we have no data but sample data exists
                    if (state.data.length === 0 && data.debug && data.debug.test_query_result && data.debug.test_query_result.length > 0) {
                        const sampleData = data.debug.test_query_result[0];
                        const sampleTimestamp = sampleData.timestamp;

                        utils.showMessage(`
                            <h3>No data found for the selected time range</h3>
                            <p>There is data in the database, but not for the specific time range you selected.</p>
                            <p>We found data at: <strong>${sampleTimestamp}</strong></p>
                            <button id="useSampleDateNowBtn" class="button button-green" style="margin-top: 10px;">
                                <span class="material-icons">schedule</span>
                                View Data Around This Time
                            </button>
                        `, 'warning');

                        // Add event listener to the button
                        document.getElementById('useSampleDateNowBtn').addEventListener('click', utils.setCurrentTime);
                    } else if (state.data.length === 0) {
                        utils.showMessage('No data found for the selected period', 'warning');
                    } else {
                        utils.showMessage(`Found ${state.data.length} records`, 'success');
                    }

                    // Update UI
                    ui.renderTable();
                    ui.updatePagination();

                    return data;
                } catch (error) {
                    console.error('Error fetching historical data:', error);
                    utils.showMessage(`Error: ${error.message}`, 'error');
                    return null;
                }
            },

            // Export data as CSV - downloads ALL pages of data
            exportCSV: async () => {
                try {
                    // Show loading message and disable the export button
                    utils.showMessage('Preparing CSV export of all data...', 'info');
                    const exportBtn = elements.exportBtn;

                    // Save original button text and disable the button
                    const originalBtnText = exportBtn.innerHTML;
                    exportBtn.disabled = true;
                    exportBtn.innerHTML = `<span class="spinner"></span> Exporting...`;

                    // Get the input values
                    const startDate = elements.startDate.value;
                    const endDate = elements.endDate.value;

                    // Validate inputs
                    if (!startDate || !endDate) {
                        utils.showMessage('Please select both start and end dates', 'error');
                        exportBtn.disabled = false;
                        exportBtn.innerHTML = originalBtnText;
                        return;
                    }

                    // Parse dates from input fields
                    const startDateObj = new Date(startDate);
                    const endDateObj = new Date(endDate);

                    // Format dates for server in YYYY-MM-DD HH:MM:SS format
                    const startFormatted = utils.formatDateForServer(startDateObj);
                    const endFormatted = utils.formatDateForServer(endDateObj);

                    // Use the backend export_csv.php endpoint to get all data at once
                    const url = `../backend/export_csv.php?parameter=all&start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}`;

                    utils.showMessage('Downloading all data as CSV...', 'info');

                    // Create a hidden link and trigger the download
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `electrical_data_${new Date().toISOString().slice(0, 10)}.csv`;
                    document.body.appendChild(a);
                    a.click();

                    // Clean up and restore button after a delay
                    setTimeout(() => {
                        document.body.removeChild(a);
                        exportBtn.disabled = false;
                        exportBtn.innerHTML = originalBtnText;
                        utils.showMessage('CSV export initiated. If the file is large, it may take a moment to download.', 'success');
                    }, 3000);
                } catch (error) {
                    console.error('Error exporting CSV:', error);
                    utils.showMessage(`Error exporting CSV: ${error.message}`, 'error');

                    // Restore button on error
                    elements.exportBtn.disabled = false;
                    elements.exportBtn.innerHTML = `<span class="material-icons">file_download</span> Export All Data (CSV)`;
                }
            }
        };

        // UI Functions
        const ui = {
            // Render the data table
            renderTable: () => {
                if (state.data.length === 0) {
                    elements.dataRows.innerHTML = '<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';
                    return;
                }

                let html = '';
                state.data.forEach(row => {
                    const timestamp = utils.formatTimestamp(row.timestamp);
                    html += `<tr>
                        <td>${timestamp}</td>
                        <td>${utils.formatNumber(row.voltage_1)}</td>
                        <td>${utils.formatNumber(row.voltage_2)}</td>
                        <td>${utils.formatNumber(row.voltage_3)}</td>
                        <td>${utils.formatNumber(row.current_1)}</td>
                        <td>${utils.formatNumber(row.current_2)}</td>
                        <td>${utils.formatNumber(row.current_3)}</td>
                        <td>${utils.formatNumber(row.pf_1)}</td>
                        <td>${utils.formatNumber(row.pf_2)}</td>
                        <td>${utils.formatNumber(row.pf_3)}</td>
                        <td>${utils.formatNumber(row.total_kw)}</td>
                        <td>${utils.formatNumber(row.frequency)}</td>
                    </tr>`;
                });

                elements.dataRows.innerHTML = html;
            },

            // Update pagination controls
            updatePagination: () => {
                elements.pageInfo.textContent = `Page ${state.currentPage} of ${state.totalPages || 1}`;
                elements.prevBtn.disabled = state.currentPage <= 1;
                elements.nextBtn.disabled = state.currentPage >= state.totalPages;
            },

            // Initialize the page
            init: () => {
                // Set default date range to current time
                utils.setCurrentTime();

                // Add event listeners
                elements.filterBtn.addEventListener('click', api.fetchHistoricalData);
                elements.exportBtn.addEventListener('click', api.exportCSV);

                elements.prevBtn.addEventListener('click', () => {
                    if (state.currentPage > 1) {
                        state.currentPage--;
                        api.fetchHistoricalData();
                    }
                });

                elements.nextBtn.addEventListener('click', () => {
                    if (state.currentPage < state.totalPages) {
                        state.currentPage++;
                        api.fetchHistoricalData();
                    }
                });
            }
        };

        // Initialize the page
        document.addEventListener('DOMContentLoaded', ui.init);
    </script>
</body>
</html>
