{"name": "router", "description": "Simple middleware-style router", "version": "2.2.0", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/router", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "devDependencies": {"finalhandler": "^2.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "run-series": "^1.1.9", "standard": "^17.1.0", "supertest": "6.3.3"}, "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 18"}, "scripts": {"lint": "standard", "test": "mocha --reporter spec --bail --check-leaks test/", "test:debug": "mocha --reporter spec --bail --check-leaks test/ --inspect --inspect-brk", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}}